// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 医生信息表
model Doctor {
  id          Int      @id @default(autoincrement())
  name        String   // 医生姓名
  title       String   // 职称
  specialty   String   // 专业领域
  experience  Int      // 从医年限
  description String   // 医生简介
  avatar      String?  // 头像URL
  education   String   // 教育背景
  achievements String  // 主要成就
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  appointments Appointment[]
  cases        Case[]
  
  @@map("doctors")
}

// 预约挂号表
model Appointment {
  id          Int      @id @default(autoincrement())
  patientName String   // 患者姓名
  phone       String   // 联系电话
  age         Int      // 年龄
  gender      String   // 性别
  symptoms    String   // 症状描述
  preferredDate DateTime // 希望就诊日期
  status      String   @default("pending") // 状态: pending, confirmed, completed, cancelled
  notes       String?  // 备注
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 关联医生
  doctorId    Int
  doctor      Doctor   @relation(fields: [doctorId], references: [id])
  
  @@map("appointments")
}

// 治疗案例表
model Case {
  id          Int      @id @default(autoincrement())
  title       String   // 案例标题
  patientAge  Int      // 患者年龄
  patientGender String // 患者性别
  diagnosis   String   // 诊断
  symptoms    String   // 症状描述
  treatment   String   // 治疗方案
  result      String   // 治疗效果
  duration    String   // 治疗周期
  images      String?  // 相关图片URLs (JSON格式)
  isPublic    Boolean  @default(true) // 是否公开展示
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 关联医生
  doctorId    Int
  doctor      Doctor   @relation(fields: [doctorId], references: [id])
  
  @@map("cases")
}

// 患者评价表
model Review {
  id          Int      @id @default(autoincrement())
  patientName String   // 患者姓名
  age         Int      // 年龄
  condition   String   // 治疗疾病
  rating      Int      // 评分 (1-5)
  content     String   // 评价内容
  treatmentDate DateTime // 治疗日期
  isPublic    Boolean  @default(true) // 是否公开展示
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("reviews")
}

// 医院信息表
model HospitalInfo {
  id          Int      @id @default(autoincrement())
  name        String   // 医院名称
  address     String   // 医院地址
  phone       String   // 联系电话
  email       String?  // 邮箱
  workingHours String  // 工作时间
  description String   // 医院简介
  services    String   // 主要服务项目
  facilities  String   // 医院设施
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("hospital_info")
}

// 新闻动态表
model News {
  id          Int      @id @default(autoincrement())
  title       String   // 新闻标题
  content     String   // 新闻内容
  summary     String   // 新闻摘要
  image       String?  // 新闻图片
  category    String   // 新闻分类
  isPublished Boolean  @default(true) // 是否发布
  publishDate DateTime @default(now()) // 发布日期
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("news")
}
