# 丁凤儒中医私家医院网站

## 项目简介

这是一个为著名乡野中医丁凤儒医生创建的私家医院网站。丁凤儒医生擅长治疗各种风湿骨病、关节炎、腰间盘突出等疾病，已为全国患者不计其数提供了专业的中医治疗服务。

## 技术栈

### 前端
- React 18 + TypeScript
- Tailwind CSS (响应式设计)
- Axios (HTTP客户端)
- React Router (路由管理)

### 后端
- Node.js + Express + TypeScript
- Prisma ORM
- SQLite 数据库
- CORS 支持

### 特性
- 前后端分离架构
- 响应式设计，支持所有主流浏览器
- RESTful API 设计
- 数据库持久化存储

## 项目结构

```
ding-fengru-hospital/
├── frontend/          # React 前端项目
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/           # Node.js 后端项目
│   ├── src/
│   ├── prisma/
│   └── package.json
└── README.md
```

## 功能模块

1. **首页展示** - 医院介绍、医生简介、特色治疗
2. **医生介绍** - 丁凤儒医生详细资料、专业背景
3. **治疗案例** - 成功治疗案例展示
4. **预约挂号** - 在线预约系统
5. **患者评价** - 真实患者反馈
6. **联系我们** - 医院地址、联系方式

## 快速开始

### 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 启动项目

```bash
# 启动后端服务 (端口: 3001)
cd backend
npm run dev

# 启动前端服务 (端口: 3000)
cd frontend
npm start
```

## 开发说明

- 后端API运行在 http://localhost:3001
- 前端应用运行在 http://localhost:3000
- 数据库文件位于 backend/prisma/dev.db

## 浏览器兼容性

支持所有现代浏览器：
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
