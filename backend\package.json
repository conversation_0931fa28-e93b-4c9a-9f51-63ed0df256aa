{"name": "ding-fengru-hospital-backend", "version": "1.0.0", "description": "Backend API for Ding Fengru Traditional Chinese Medicine Hospital", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "ts-node prisma/seed.ts"}, "keywords": ["hospital", "tcm", "api", "express"], "author": "Developer", "license": "MIT", "dependencies": {"@prisma/client": "^5.0.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/node": "^20.4.0", "prisma": "^5.0.0", "typescript": "^5.1.6", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0"}}