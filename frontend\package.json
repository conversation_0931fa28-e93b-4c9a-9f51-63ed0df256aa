{"name": "ding-fengru-hospital-frontend", "version": "1.0.0", "description": "Frontend for Ding Fengru Traditional Chinese Medicine Hospital", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.0", "axios": "^1.4.0", "react-scripts": "5.0.1", "web-vitals": "^3.3.2"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.4.0", "typescript": "^5.1.6", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}